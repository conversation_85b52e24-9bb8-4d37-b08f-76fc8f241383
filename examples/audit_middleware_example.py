"""
审计中间件使用示例

演示如何在FastAPI应用中配置和使用审计中间件来自动记录API操作的审计日志。
"""

from fastapi import FastAPI, Depends, Request, HTTPException
from pydantic import BaseModel
from typing import Optional

# 导入审计中间件
from svc.core.middleware.audit import AuditMiddleware
from svc.core.middleware import setup_middlewares, middleware_config

# 创建FastAPI应用
app = FastAPI(title="审计中间件示例", version="1.0.0")

# 模拟用户模型
class User(BaseModel):
    id: int
    username: str
    email: str

class UserCreate(BaseModel):
    username: str
    email: str

class UserUpdate(BaseModel):
    username: Optional[str] = None
    email: Optional[str] = None

# 模拟当前用户依赖
async def get_current_user(request: Request) -> User:
    """模拟获取当前用户"""
    # 在实际应用中，这里会从JWT token或session中获取用户信息
    # 为了演示，我们模拟一个用户并设置到request.state中
    user = User(id=1, username="demo_user", email="<EMAIL>")
    
    # 设置用户信息到request.state（审计中间件会使用这些信息）
    request.state.user_id = user.id
    request.state.token_payload = {"username": user.username}
    
    return user

# 模拟数据存储
users_db = {
    1: User(id=1, username="admin", email="<EMAIL>"),
    2: User(id=2, username="user1", email="<EMAIL>"),
}
next_user_id = 3

# API路由
@app.get("/")
async def root():
    """根路径 - 不会被审计（GET请求默认被排除）"""
    return {"message": "审计中间件示例应用"}

@app.get("/health")
async def health():
    """健康检查 - 不会被审计（在排除路径中）"""
    return {"status": "healthy"}

@app.get("/api/users")
async def list_users(current_user: User = Depends(get_current_user)):
    """获取用户列表 - 不会被审计（GET请求默认被排除）"""
    return {"users": list(users_db.values())}

@app.get("/api/users/{user_id}")
async def get_user(user_id: int, current_user: User = Depends(get_current_user)):
    """获取单个用户 - 不会被审计（GET请求默认被排除）"""
    if user_id not in users_db:
        raise HTTPException(status_code=404, detail="用户不存在")
    return users_db[user_id]

@app.post("/api/users")
async def create_user(
    user_data: UserCreate,
    request: Request,
    current_user: User = Depends(get_current_user)
):
    """创建用户 - 会被审计记录"""
    global next_user_id
    
    # 检查用户名是否已存在
    for user in users_db.values():
        if user.username == user_data.username:
            raise HTTPException(status_code=400, detail="用户名已存在")
    
    # 创建新用户
    new_user = User(
        id=next_user_id,
        username=user_data.username,
        email=user_data.email
    )
    users_db[next_user_id] = new_user
    next_user_id += 1
    
    return {
        "message": "用户创建成功",
        "user": new_user
    }

@app.put("/api/users/{user_id}")
async def update_user(
    user_id: int,
    user_data: UserUpdate,
    request: Request,
    current_user: User = Depends(get_current_user)
):
    """更新用户 - 会被审计记录"""
    if user_id not in users_db:
        raise HTTPException(status_code=404, detail="用户不存在")
    
    user = users_db[user_id]
    
    # 更新用户信息
    if user_data.username is not None:
        user.username = user_data.username
    if user_data.email is not None:
        user.email = user_data.email
    
    users_db[user_id] = user
    
    return {
        "message": "用户更新成功",
        "user": user
    }

@app.delete("/api/users/{user_id}")
async def delete_user(
    user_id: int,
    request: Request,
    current_user: User = Depends(get_current_user)
):
    """删除用户 - 会被审计记录"""
    if user_id not in users_db:
        raise HTTPException(status_code=404, detail="用户不存在")
    
    deleted_user = users_db.pop(user_id)
    
    return {
        "message": "用户删除成功",
        "deleted_user": deleted_user
    }

@app.post("/api/users/{user_id}/activate")
async def activate_user(
    user_id: int,
    request: Request,
    current_user: User = Depends(get_current_user)
):
    """激活用户 - 会被审计记录"""
    if user_id not in users_db:
        raise HTTPException(status_code=404, detail="用户不存在")
    
    # 模拟激活操作
    return {
        "message": f"用户 {user_id} 已激活",
        "user_id": user_id
    }

# 配置中间件
def setup_audit_middleware():
    """配置审计中间件"""
    
    # 方式1: 直接添加中间件
    app.add_middleware(
        AuditMiddleware,
        enabled=True,
        exclude_paths={
            "/", "/health", "/docs", "/openapi.json", "/redoc",
            "/system/audit"  # 避免审计日志查询产生循环记录
        },
        exclude_methods={"GET", "OPTIONS", "HEAD"},  # 只记录修改操作
        include_request_body=True,  # 包含请求体
        include_response_body=False,  # 不包含响应体
        max_body_size=2048  # 最大记录2KB的请求体
    )
    
    # 方式2: 使用配置文件方式（注释掉，因为已经用方式1了）
    # custom_config = middleware_config.copy()
    # custom_config["audit"]["enabled"] = True
    # custom_config["audit"]["options"]["include_request_body"] = True
    # setup_middlewares(app, custom_config)

# 应用启动事件
@app.on_event("startup")
async def startup_event():
    """应用启动时的初始化"""
    print("🚀 审计中间件示例应用启动")
    print("📝 审计中间件已启用，将记录以下操作：")
    print("   - POST /api/users (创建用户)")
    print("   - PUT /api/users/{user_id} (更新用户)")
    print("   - DELETE /api/users/{user_id} (删除用户)")
    print("   - POST /api/users/{user_id}/activate (激活用户)")
    print("💡 GET请求和健康检查不会被记录")

# 设置审计中间件
setup_audit_middleware()

if __name__ == "__main__":
    import uvicorn
    
    print("启动审计中间件示例应用...")
    print("访问 http://localhost:8000/docs 查看API文档")
    print("尝试以下操作来生成审计日志：")
    print("1. POST /api/users - 创建用户")
    print("2. PUT /api/users/1 - 更新用户")
    print("3. DELETE /api/users/2 - 删除用户")
    print("4. POST /api/users/1/activate - 激活用户")
    
    uvicorn.run(
        "audit_middleware_example:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
